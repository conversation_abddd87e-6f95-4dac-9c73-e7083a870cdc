{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"basic@../../commons/basic": "basic@../../commons/basic", "hsmeeting_hmos_sdk@../library": "hsmeeting_hmos_sdk@../library", "libcapture.so@../library/src/main/cpp/types/capture": "libcapture.so@../library/src/main/cpp/types/capture", "libdesktopshare.so@../library/src/main/cpp/types/desktopshare": "libdesktopshare.so@../library/src/main/cpp/types/desktopshare", "libencoder.so@../library/src/main/cpp/types/encoder": "libencoder.so@../library/src/main/cpp/types/encoder", "libentry.so@../library/src/main/cpp/types/libentry": "libentry.so@../library/src/main/cpp/types/libentry", "libplayer.so@../library/src/main/cpp/types/decoder": "libplayer.so@../library/src/main/cpp/types/decoder", "routermoudel@../../commons/RouterMoudel": "routermoudel@../../commons/RouterMoudel", "schedulemeeting@../schedulemeeting": "schedulemeeting@../schedulemeeting"}, "packages": {"basic@../../commons/basic": {"name": "basic", "version": "1.0.0", "resolved": "../../commons/basic", "registryType": "local", "packageType": "InterfaceHar"}, "hsmeeting_hmos_sdk@../library": {"name": "hsmeeting_hmos_sdk", "version": "1.0.0", "resolved": "../library", "registryType": "local", "dependencies": {"libentry.so": "file:./src/main/cpp/types/libentry", "libencoder.so": "file:./src/main/cpp/types/encoder", "libplayer.so": "file:./src/main/cpp/types/decoder", "libdesktopshare.so": "file:./src/main/cpp/types/desktopshare", "libcapture.so": "file:./src/main/cpp/types/capture", "basic": "file:../../commons/basic"}}, "libcapture.so@../library/src/main/cpp/types/capture": {"name": "libcapture.so", "version": "1.0.0", "resolved": "../library/src/main/cpp/types/capture", "registryType": "local"}, "libdesktopshare.so@../library/src/main/cpp/types/desktopshare": {"name": "libdesktopshare.so", "version": "1.0.0", "resolved": "../library/src/main/cpp/types/desktopshare", "registryType": "local"}, "libencoder.so@../library/src/main/cpp/types/encoder": {"name": "libencoder.so", "version": "1.0.0", "resolved": "../library/src/main/cpp/types/encoder", "registryType": "local"}, "libentry.so@../library/src/main/cpp/types/libentry": {"name": "libentry.so", "version": "1.0.0", "resolved": "../library/src/main/cpp/types/libentry", "registryType": "local"}, "libplayer.so@../library/src/main/cpp/types/decoder": {"name": "libplayer.so", "version": "1.0.0", "resolved": "../library/src/main/cpp/types/decoder", "registryType": "local"}, "routermoudel@../../commons/RouterMoudel": {"name": "routermoudel", "version": "1.0.0", "resolved": "../../commons/RouterMoudel", "registryType": "local"}, "schedulemeeting@../schedulemeeting": {"name": "schedulemeeting", "version": "1.0.0", "resolved": "../schedulemeeting", "registryType": "local", "dependencies": {"hsmeeting_hmos_sdk": "file:../library", "basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}}}