import { BuilderNameConstants, RouterModule, RouterNameConstants } from 'routermoudel';
import { promptAction } from '@kit.ArkUI';
import { UserInfo, serverConfig } from 'basic';
import { MeetingService, MeetingData } from 'schedulemeeting';
// import { joinConf } from 'hsmeeting_hmos_sdk';
// import { parseNameValuePairs, MeetingInfo } from 'hsmeeting_hmos_sdk';

@Component
export struct CreateMeeting {
  @State meetingTitle: string = '';
  @State meetingPassword: string = '';
  @State isCreating: boolean = false;

  // 获取当前用户的显示名称
  private getCurrentUserDisplayName(): string {
    const userInfo = UserInfo.getInstance();
    if (userInfo.isLoggedIn()) {
      return userInfo.userName || userInfo.realName || userInfo.nickName || '用户';
    }
    return '匿名用户';
  }

  // 获取服务器地址
  private getServerUrl(): string {
    return serverConfig.getServerUrl();
  }

  // 创建会议并加入
  private async createAndJoinMeeting() {
    if (this.isCreating) return;

    // 检查登录状态
    if (!UserInfo.getInstance().isLoggedIn()) {
      promptAction.showToast({ message: '匿名用户不能操作此功能' });
      return;
    }

    if (this.meetingTitle.trim() === '') {
      promptAction.showToast({ message: '请输入会议主题' });
      return;
    }

    const serverUrl = this.getServerUrl();
    if (!serverUrl || serverUrl.trim() === '') {
      promptAction.showToast({ message: '请先在设置中配置服务器地址' });
      return;
    }

    this.isCreating = true;

    try {
      // 第一步：创建会议（预约会议）
      const meetingService: MeetingService = MeetingService.getInstance();

      // 获取当前时间作为开始时间，结束时间设为1小时后
      const now: Date = new Date();
      const startTime: string = now.toISOString().slice(0, 19).replace('T', ' ');
      const endTime: string = new Date(now.getTime() + 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ');

      const meetingData: MeetingData = {
        subject: this.meetingTitle,
        startTime: startTime,
        endTime: endTime,
        attendeeAmount: 10,
        hostName: 'admin',
        creator: 'admin',
        openType: true,
        passwd: this.meetingPassword || '123456',
        conferencePattern: 0, // 主持人模式
        agenda: `会议主题：${this.meetingTitle}`
      };

      console.log('创建会议数据:', JSON.stringify(meetingData));
      const createResult: string = await meetingService.scheduleMeeting(meetingData);
      console.log('会议创建结果:', createResult);

      // 解析创建会议的响应，获取会议号
      // const meetingInfo: MeetingInfo = parseNameValuePairs(createResult);
      // console.log('解析的会议信息:', JSON.stringify(meetingInfo));
      //
      // if (meetingInfo.return !== '0') {
      //   throw new Error('创建会议失败');
      // }
      //
      // if (!meetingInfo.confId) {
      //   throw new Error('未获取到会议号');
      // }
      //
      // promptAction.showToast({ message: '会议创建成功，正在加入...' });
      //
      // // 第二步：以主持人身份加入会议
      // const displayName: string = this.getCurrentUserDisplayName();
      //
      // console.info(`以主持人身份加入会议: 会议号=${meetingInfo.confId}, 显示名=${displayName}, 服务器=${serverUrl}`);

      // await joinConf(
      //   meetingInfo.confId,           // confId - 会议号
      //   this.meetingPassword || '123456', // confPwd - 会议密码
      //   displayName,                  // joinName - 显示名
      //   2,                           // confType - 会议类型，2表示视频会议
      //   0,                           // role - 角色，0表示主持人
      //   serverUrl                    // siteUrl - 服务器地址
      // );

      promptAction.showToast({ message: '成功加入会议' });

    } catch (error) {
      console.error('创建或加入会议失败:', error);
      let errorMessage: string = '创建会议失败';
      if (error instanceof Error) {
        errorMessage = `创建会议失败: ${error.message}`;
      }
      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isCreating = false;
    }
  }

  build() {
    NavDestination() {
      Column() {
        // 表单内容
        Column() {
          // 会议主题
          Column() {
            Row() {
              Text('会议主题')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: '会议主题', text: this.meetingTitle })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .onChange((value: string) => {
                  this.meetingTitle = value;
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)

          // 分割线
          Divider()
            .strokeWidth(0.5)
            .color('#E5E5E5')
            .margin({ left: 16, right: 16 })

          // 会议密码
          Column() {
            Row() {
              Text('会议密码')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: '请输入密码', text: this.meetingPassword })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .type(InputType.Password)
                .onChange((value: string) => {
                  this.meetingPassword = value;
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)
        }
        .backgroundColor(Color.White)
        .borderRadius(12)
        .margin({ top: 20, left: 16, right: 16 })

        Blank()

        // 底部按钮
        Column() {
          Button(this.isCreating ? '创建中...' : '发起会议')
            .width('100%')
            .height(50)
            .fontSize(18)
            .fontColor(Color.White)
            .backgroundColor(this.isCreating ? '#CCCCCC' : '#007AFF')
            .borderRadius(12)
            .fontWeight(FontWeight.Medium)
            .enabled(!this.isCreating)
            .onClick(() => {
              this.createAndJoinMeeting();
            })
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 34 })
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F5F5F5')
    }
    .title('发起会议')
    .onBackPressed(() => {
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

// 注册构建器
@Builder
export function CreateMeeting_Page(value: object) {
  CreateMeeting()
}

const builderName = BuilderNameConstants.CreateMeetingPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(CreateMeeting_Page);
  RouterModule.registerBuilder(builderName, builder);
}