import { BuilderNameConstants, builderRouterModel, RouterModule, RouterNameConstants } from 'routermoudel';
import { promptAction } from '@kit.ArkUI';
import { serverConfig } from 'basic';
import { joinConf } from 'hsmeeting_hmos_sdk';

@Component
export struct JoinMeetingPage {
  @State meetingId: string = '';
  @State displayName: string = '';
  @State meetingPassword: string = '';
  @State isLoading: boolean = false;
  
  // 输入框焦点状态
  @State meetingIdFocused: boolean = false;
  @State displayNameFocused: boolean = false;
  @State passwordFocused: boolean = false;
  
  // 错误状态
  @State meetingIdError: string = '';
  @State displayNameError: string = '';
  @State passwordError: string = '';
  @State isFormValid: boolean = false;

  // 验证会议号
  private validateMeetingId(value: string): string {
    if (!value || value.trim() === '') {
      return '请输入会议号';
    }
    if (!/^\d+$/.test(value.trim())) {
      return '会议号只能包含数字';
    }
    return '';
  }

  // 验证显示名
  private validateDisplayName(value: string): string {
    if (!value || value.trim() === '') {
      return '请输入显示名';
    }
    if (value.trim().length < 2) {
      return '显示名至少需要2个字符';
    }
    return '';
  }

  // 验证密码（可选）
  private validatePassword(value: string): string {
    // 密码是可选的，所以空值是有效的
    return '';
  }

  // 更新表单验证状态
  private updateFormValidation(): void {
    this.isFormValid = 
      this.meetingId.trim() !== '' && 
      this.displayName.trim() !== '' &&
      this.meetingIdError === '' && 
      this.displayNameError === '' && 
      this.passwordError === '';
  }

  // 获取服务器地址
  private getServerUrl(): string {
    try {
      // 使用ServerConfig获取服务器地址，确保数据一致性
      return serverConfig.getServerUrl();
    } catch (error) {
      console.error('获取服务器地址失败:', error);
      return ''; // 移除硬编码的qa27地址
    }
  }

  // 获取缓存的用户名
  private getCachedUsername(): string {
    try {
      const cachedUsername = AppStorage.Get<string>('cachedUsername');
      if (cachedUsername && cachedUsername.trim() !== '') {
        return cachedUsername.trim();
      }
    } catch (error) {
      console.error('获取缓存用户名失败:', error);
    }
    return '请输入用户名';
  }

  // 组件初始化时加载缓存的用户名
  aboutToAppear(): void {
    try {
      const cachedUsername = AppStorage.Get<string>('cachedUsername');
      if (cachedUsername && cachedUsername.trim() !== '' && this.displayName === '') {
        this.displayName = cachedUsername.trim();
        this.updateFormValidation();
      }
    } catch (error) {
      console.error('加载缓存用户名失败:', error);
    }
  }

  // 处理加入会议
  private async handleJoinMeeting(): Promise<void> {
    if (!this.isFormValid || this.isLoading) {
      return;
    }

    // 最终验证
    const meetingIdError = this.validateMeetingId(this.meetingId);
    const displayNameError = this.validateDisplayName(this.displayName);
    
    if (meetingIdError || displayNameError) {
      this.meetingIdError = meetingIdError;
      this.displayNameError = displayNameError;
      return;
    }

    this.isLoading = true;

    try {
      const serverUrl = this.getServerUrl();

      if (!serverUrl || serverUrl.trim() === '') {
        promptAction.showToast({
          message: '请先在设置中配置服务器地址'
        });
        return;
      }

      console.info(`加入会议: 会议号=${this.meetingId}, 显示名=${this.displayName}, 服务器=${serverUrl}`);

      // 调用加入会议函数
      await joinConf(
        this.meetingId.trim(),        // confId - 会议号
        this.meetingPassword.trim(),  // confPwd - 会议密码
        this.displayName.trim(),      // joinName - 显示名
        2,                           // confType - 会议类型，2表示视频会议
        1,                           // role - 角色，1表示参加者，0表示主持人
        serverUrl                    // siteUrl - 服务器地址
      );

      promptAction.showToast({
        message: '正在加入会议...'
      });

    } catch (error) {
      console.error('加入会议失败:', error);
      promptAction.showToast({ 
        message: '加入会议失败，请检查会议号和网络连接' 
      });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    NavDestination() {
      Column() {
        // 表单内容
        List() {
          // 会议号输入框
          ListItem() {
            Row() {
              Text('会议号')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: '请输入会议号', text: this.meetingId })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .onChange((value: string) => {
                  this.meetingId = value;
                  this.meetingIdError = this.validateMeetingId(this.meetingId);
                  this.updateFormValidation();
                })
                .onBlur(() => {
                  this.meetingIdFocused = false;
                  this.meetingIdError = this.validateMeetingId(this.meetingId);
                  this.updateFormValidation();
                })
                .onFocus(() => {
                  this.meetingIdFocused = true;
                  if (this.meetingIdError) {
                    this.meetingIdError = '';
                    this.updateFormValidation();
                  }
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)

          // 分割线
          ListItem() {
            Divider()
              .strokeWidth(0.5)
              .color('#E5E5E5')
              .margin({ left: 16, right: 16 })
          }
          .height(0.5)

          // 显示名输入框
          ListItem() {
            Row() {
              Text('显示名')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: this.getCachedUsername(), text: this.displayName })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .onChange((value: string) => {
                  this.displayName = value;
                  this.displayNameError = this.validateDisplayName(this.displayName);
                  this.updateFormValidation();
                })
                .onBlur(() => {
                  this.displayNameFocused = false;
                  this.displayNameError = this.validateDisplayName(this.displayName);
                  this.updateFormValidation();
                })
                .onFocus(() => {
                  this.displayNameFocused = true;
                  if (this.displayNameError) {
                    this.displayNameError = '';
                    this.updateFormValidation();
                  }
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)

          // 分割线
          ListItem() {
            Divider()
              .strokeWidth(0.5)
              .color('#E5E5E5')
              .margin({ left: 16, right: 16 })
          }
          .height(0.5)

          // 会议密码输入框
          ListItem() {
            Row() {
              Text('会议密码')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)

              Blank()

              TextInput({ placeholder: '请输入密码', text: this.meetingPassword })
                .width(200)
                .fontSize(16)
                .fontColor('#333333')
                .backgroundColor(Color.Transparent)
                .placeholderColor('#CCCCCC')
                .textAlign(TextAlign.End)
                .border({ width: 0 })
                .type(InputType.Password)
                .onChange((value: string) => {
                  this.meetingPassword = value;
                  this.passwordError = this.validatePassword(this.meetingPassword);
                  this.updateFormValidation();
                })
                .onBlur(() => {
                  this.passwordFocused = false;
                  this.passwordError = this.validatePassword(this.meetingPassword);
                  this.updateFormValidation();
                })
                .onFocus(() => {
                  this.passwordFocused = true;
                  if (this.passwordError) {
                    this.passwordError = '';
                    this.updateFormValidation();
                  }
                })
            }
            .width('100%')
            .height(50)
            .padding({ left: 16, right: 16 })
            .alignItems(VerticalAlign.Center)
          }
          .backgroundColor(Color.White)
          .borderRadius(0)
        }
        .backgroundColor(Color.White)
        .borderRadius(12)
        .margin({ top: 20, left: 16, right: 16 })

        Blank()

        // 底部按钮
        Column() {
          Button(this.isLoading ? '加入中...' : '加入会议')
            .width('100%')
            .height(50)
            .fontSize(18)
            .fontColor(Color.White)
            .backgroundColor(this.isFormValid && !this.isLoading ? '#007AFF' : '#CCCCCC')
            .borderRadius(12)
            .fontWeight(FontWeight.Medium)
            .enabled(this.isFormValid && !this.isLoading)
            .onClick(() => {
              this.handleJoinMeeting();
            })
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 34 })
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F5F5F5')
    }
    .title('加入会议')
    .onBackPressed(() => {
      RouterModule.pop(RouterNameConstants.HomeIndex);
      return true;
    })
  }
}

@Builder
export function JoinMeeting_Page(value: object) {
  JoinMeetingPage()
}


const builderName = BuilderNameConstants.JoinMeetingPage;
if (!RouterModule.getBuilder(builderName)) {
  const builder: WrappedBuilder<[object]> = wrapBuilder(JoinMeeting_Page);
  RouterModule.registerBuilder(builderName, builder);
}