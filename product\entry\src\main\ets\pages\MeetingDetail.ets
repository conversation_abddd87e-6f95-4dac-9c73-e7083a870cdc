import router from '@ohos.router'
import { loginApi, serverConfig } from 'basic'
import { joinConf } from 'hsmeeting_hmos_sdk'
import { promptAction } from '@kit.ArkUI'

// 定义会议信息接口
interface MeetingInfo {
  meetingId: string
  displayName: string
  password: string
  title: string
  startTime: string
  endTime: string
  duration: string
  host: string
}

@Entry
@Component
struct MeetingDetail {
  // 获取当前用户的默认显示名
  private getCurrentUserDisplayName(): string {
    try {
      const currentUser = loginApi.getCurrentUser();

      // 如果是匿名用户，返回空字符串（不显示）
      if (currentUser.isAnonymousUser()) {
        return '';
      }

      // 优先使用真实姓名，其次是昵称，最后是用户名
      if (currentUser.realName && currentUser.realName.trim() !== '') {
        return currentUser.realName.trim();
      }

      if (currentUser.nickName && currentUser.nickName.trim() !== '') {
        return currentUser.nickName.trim();
      }

      if (currentUser.userName && currentUser.userName.trim() !== '') {
        return currentUser.userName.trim();
      }

      // 如果都没有，返回默认值
      return '用户';
    } catch (error) {
      console.error('获取用户显示名失败:', error);
      return '用户';
    }
  }

  // 添加静态假数据,指定类型为MeetingInfo
  private meeting: MeetingInfo = {
    meetingId: '12721922',          // 会议号
    displayName: this.getCurrentUserDisplayName(), // 使用当前用户的默认显示名
    password: '123456',             // 会议密码
    title: '测试会议',              // 会议标题
    startTime: '2024-03-20 14:30',  // 开始时间
    endTime: '2024-03-20 15:30',    // 结束时间
    duration: '1小时',              // 会议时长
    host: '测试1'                   // 主持人
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.back'))
          .width(24)
          .height(24)
          .margin({ left: 16 })
          .onClick(() => {
            router.back()
          })
        Text('会议详情')
          .fontSize(20)
          .fontWeight(FontWeight.Medium)
          .margin({ left: 16 })
      }
      .width('100%')
      .height(56)
      .alignItems(VerticalAlign.Center)
      .backgroundColor(Color.White)

      // 会议标题
      Text(this.meeting.title)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 20, bottom: 20, left: 16, right: 16 })
        .width('100%')

      // 会议时间显示
      Row() {
        Column() {
          Text('14:30')
            .fontSize(36)
            .fontWeight(FontWeight.Bold)
          Text('2024年03月20日')
            .fontSize(16)
            .margin({ top: 8 })
        }
        .alignItems(HorizontalAlign.Center)

        Column() {
          Text('1小时')
            .fontSize(14)
            .backgroundColor('#F5F5F5')
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .borderRadius(4)
        }
        .margin({ left: 20, right: 20 })

        Column() {
          Text('15:30')
            .fontSize(36)
            .fontWeight(FontWeight.Bold)
          Text('2024年03月20日')
            .fontSize(16)
            .margin({ top: 8 })
        }
        .alignItems(HorizontalAlign.Center)
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .margin({ bottom: 20 })

      // 会议信息列表
      List() {
        ListItem() {
          this.InfoItem('会议号', this.meeting.meetingId)
        }

        ListItem() {
          this.InfoItem('主持人名称', this.meeting.host)
        }

        ListItem() {
          this.InfoItem('显示名', this.meeting.displayName)
        }

        ListItem() {
          Column() {
            this.InfoItem('会议链接', '会议链接将根据服务器配置动态生成')
            Text('复制')
              .fontSize(14)
              .fontColor(Color.Blue)
              .margin({ top: 8 })
          }
        }
      }
      .backgroundColor(Color.White)
      .divider({ strokeWidth: 1, color: '#F5F5F5' })

      // 底部按钮
      Button('加入会议', { type: ButtonType.Normal })
        .width('90%')
        .height(50)
        .fontSize(16)
        .backgroundColor('#0A59F7')
        .margin({ top: 40 })
        .onClick(async () => {
          try {
            const serverUrl = serverConfig.getServerUrl();

            if (!serverUrl || serverUrl.trim() === '') {
              promptAction.showToast({
                message: '请先在设置中配置服务器地址'
              });
              return;
            }

            if (!this.meeting.displayName || this.meeting.displayName.trim() === '') {
              promptAction.showToast({
                message: '请输入显示名'
              });
              return;
            }

            console.info(`加入会议: 会议号=${this.meeting.meetingId}, 显示名=${this.meeting.displayName}, 服务器=${serverUrl}`);

            // 调用加入会议函数
            await joinConf(
              this.meeting.meetingId,      // confId - 会议号
              this.meeting.password,       // confPwd - 会议密码
              this.meeting.displayName,    // joinName - 显示名
              2,                          // confType - 会议类型，2表示视频会议
              1,                          // role - 角色，1表示参加者，0表示主持人
              serverUrl                   // siteUrl - 服务器地址
            );

            promptAction.showToast({
              message: '正在加入会议...'
            });

          } catch (error) {
            console.error('加入会议失败:', error);
            promptAction.showToast({
              message: '加入会议失败，请检查网络连接'
            });
          }
        })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  InfoItem(title: string, content: string) {
    Row() {
      Text(title)
        .fontSize(16)
        .fontColor('#666666')
      Text(content)
        .fontSize(16)
        .margin({ left: 16 })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 16, bottom: 16 })
    .justifyContent(FlexAlign.SpaceBetween)
  }
}